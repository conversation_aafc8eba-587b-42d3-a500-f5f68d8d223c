# encoding:utf-8
import io
import plugins
from bridge.context import ContextType
from bridge.reply import Reply, ReplyType
from channel.chat_message import ChatMessage
from common.log import logger
from plugins import *
from config import conf
from .misc import *
import xml.etree.ElementTree as ET
from common.expired_dict import ExpiredDict
from PIL import Image, ImageFilter
from io import BytesIO

from common.tmp_dir import TmpDir


@plugins.register(
    name="Ref",
    desire_priority=9,
    hidden=True,
    desc="A simple plugin that do ref test",
    version="0.1",
    author="fred",
)
class Ref(Plugin):
    def __init__(self):
        super().__init__()
        try:
            self.config = super().load_config()
            if not self.config:
                self.config = self._load_config_template()
            self.zhipu_api_key = self.config.get("zhipu_api_key")
            self.zhipu_image_model = self.config.get("zhipu_image_model")
            logger.info("[Ref] inited")
            self.handlers[Event.ON_HANDLE_CONTEXT] = self.on_handle_context
        except Exception as e:
            logger.error(f"[Ref]初始化异常：{e}")
            raise "[Ref] init failed, ignore "
        self.msg_cache = ExpiredDict(60 * 3)

    def handle_ref(self, e_context):
        try:
            if not is_gewe():
                return
            msg = e_context["context"]["msg"]
            raw_msg = msg.msg
            msg_type = raw_msg["Data"]["MsgType"]
            msg_id = str(raw_msg["Data"]["NewMsgId"])
            # cache image & emoji msg
            if msg_type == 3 or msg_type == 47:
                self.msg_cache[msg_id] = msg

            # ref: https://github.com/LC044/WeChatMsg/blob/master/doc/%E6%95%B0%E6%8D%AE%E5%BA%93%E4%BB%8B%E7%BB%8D.md
            if msg_type == 49:
                try:
                    content_xml = raw_msg["Data"]["Content"]["string"]
                    # Find the position of '<?xml' declaration and remove any prefix
                    xml_start = content_xml.find("<?xml")
                    if xml_start == -1:
                        logger.error("[Ref] Invalid XML content: XML declaration not found")
                        return
                    if xml_start != 0:
                        logger.warning("[Ref] XML content has prefix, cleaning...")
                        content_xml = content_xml[xml_start:]
                    
                    try:
                        root = ET.fromstring(content_xml)
                    except ET.ParseError as e:
                        logger.error(f"[Ref] XML parsing error: {str(e)}")
                        return
                        
                    appmsg = root.find("appmsg")
                    if appmsg is None:
                        logger.error("[Ref] Invalid XML structure: appmsg not found")
                        return
                        
                    msg_type = appmsg.find("type")
                    if msg_type is None or msg_type.text != "57":
                        return
                        
                    title = appmsg.find("title")
                    if title is None or not title.text:
                        logger.error("[Ref] Invalid XML structure: title not found or empty")
                        return
                    title_text = title.text.strip()
                    
                    if msg.is_group:
                        logger.debug(f"[Ref]: in group{title_text}")
                        import re
                        title_text = re.sub(r"@[^\u2005]+\u2005", "", title_text)
                    title_text = title_text.strip()
                    
                    refermsg = appmsg.find("refermsg")
                    if refermsg is None:
                        logger.error("[Ref] Invalid XML structure: refermsg not found")
                        return
                        
                    ref_type = refermsg.find("type")
                    svrid = refermsg.find("svrid")
                    if ref_type is None or svrid is None:
                        logger.error("[Ref] Invalid XML structure: type or svrid not found")
                        return
                        
                    ref_type = ref_type.text
                    svrid = svrid.text
                    
                    # Process different reference types
                    if ref_type == "1":  # TEXT
                        if title_text in ["举", "举牌"]:
                            quoted_content = refermsg.find("content")
                            if quoted_content is None or not quoted_content.text:
                                logger.error("[Ref] Invalid XML structure: content not found or empty")
                                return
                            image_url = get_card_image_url(quoted_content.text)
                            if image_url:
                                try:
                                    image_data = download_image_by_url(image_url)
                                    if image_data:
                                        image_data.seek(0)
                                        image = Image.open(image_data)
                                        jpg_image_data = BytesIO()
                                        image.convert("RGB").save(jpg_image_data, format="JPEG")
                                        jpg_image_data.seek(0)
                                        
                                        reply = Reply()
                                        reply.type = ReplyType.IMAGE
                                        reply.content = jpg_image_data
                                        e_context["reply"] = reply
                                        e_context.action = EventAction.BREAK_PASS
                                except Exception as e:
                                    logger.error(f"[Ref] Error processing image: {str(e)}")
                                    return
                                    
                    elif ref_type == "49":  # Chat History
                        try:
                            content_node = refermsg.find("content")
                            if content_node is not None and content_node.text:
                                inner_msg = ET.fromstring(content_node.text)
                                inner_appmsg = inner_msg.find("appmsg")
                                if inner_appmsg is not None:
                                    des = inner_appmsg.find("des")
                                    if des is not None and des.text:
                                        chat_content = des.text.strip()
                                        if title_text in ["总结", "分析"]:
                                            try:
                                                from zhipuai import ZhipuAI
                                                
                                                zhipu_api_key = self.zhipu_api_key
                                                if is_none_or_empty(zhipu_api_key):
                                                    set_reply_text("API key未设置", e_context, ReplyType.INFO)
                                                    return
                                                
                                                prompts = {
                                                    "总结": """请对以下聊天记录进行精炼总结：
1. 提取核心对话内容和关键信息
2. 用简洁的语言概括对话主题
3. 突出重要的结论或决定
4. 保持客观中立的语气
5. 如果是群聊，注意总结各方观点

聊天记录内容如下：
{content}""",
                                                    "分析": """请对以下聊天记录进行深入分析：
1. 分析对话的主要目的和背景
2. 提取对话中的关键论点和观点
3. 识别对话中的情感倾向和态度
4. 总结对话各方的互动模式
5. 如果有争议，分析各方立场
6. 对对话的效果和结果进行评估
7. 如果是群聊，分析群组互动动态

聊天记录内容如下：
{content}"""
                                                }
                                                
                                                prompt = prompts.get(title_text, "请分析以下聊天记录的内容，总结对话要点：\n{content}")
                                                prompt = prompt.format(content=chat_content)
                                                
                                                client = ZhipuAI(api_key=zhipu_api_key)
                                                response = client.chat.completions.create(
                                                    model="glm-4-flash",
                                                    messages=[
                                                        {
                                                            "role": "user",
                                                            "content": prompt
                                                        }
                                                    ],
                                                )
                                                
                                                if not response or not response.choices:
                                                    raise ValueError("Empty response from ZhipuAI")
                                                    
                                                finish_reason = response.choices[0].finish_reason
                                                if finish_reason != "stop":
                                                    raise ValueError(f"Unexpected finish reason: {finish_reason}")
                                                    
                                                reply_content = response.choices[0].message.content
                                                if not reply_content:
                                                    raise ValueError("Empty reply content from ZhipuAI")
                                                    
                                                logger.info("[Ref] Successfully got analysis from ZhipuAI")
                                                set_reply_text(reply_content, e_context, ReplyType.TEXT)
                                                
                                            except Exception as e:
                                                logger.error(f"[Ref] Error in chat analysis: {str(e)}")
                                                set_reply_text("聊天记录分析失败", e_context, ReplyType.ERROR)
                                                return
                                    else:
                                        logger.error("[Ref] No des node found in inner appmsg")
                                else:
                                    logger.error("[Ref] No inner appmsg found")
                            else:
                                logger.error("[Ref] No content found in refermsg")
                        except Exception as e:
                            logger.error(f"[Ref] Error processing chat history: {str(e)}")
                            return
                            
                    elif ref_type == "3":  # IMAGE
                        try:
                            filters = {
                                "BLUR": ImageFilter.BLUR,
                                "CONTOUR": ImageFilter.CONTOUR,
                                "DETAIL": ImageFilter.DETAIL,
                                "EDGE_ENHANCE": ImageFilter.EDGE_ENHANCE,
                                "EDGE_ENHANCE_MORE": ImageFilter.EDGE_ENHANCE_MORE,
                                "EMBOSS": ImageFilter.EMBOSS,
                                "FIND_EDGES": ImageFilter.FIND_EDGES,
                                "SHARPEN": ImageFilter.SHARPEN,
                                "SMOOTH": ImageFilter.SMOOTH,
                                "SMOOTH_MORE": ImageFilter.SMOOTH_MORE,
                            }

                            path_image = None
                            ref_image_msg = self.msg_cache.get(svrid)
                            if ref_image_msg:
                                try:
                                    ref_image_msg.prepare()
                                    path_image = ref_image_msg.content
                                    logger.debug(f"[Ref] Found image in cache: {path_image}")
                                except Exception as e:
                                    logger.error(f"[Ref] Error preparing cached image: {str(e)}")
                            
                            if not path_image:
                                try:
                                    # 尝试从引用消息中提取图片内容
                                    content_node = refermsg.find("content")
                                    if content_node is not None and content_node.text:
                                        from .misc import download_image
                                        path_image_me = os.path.join(TmpDir().path(), f"{svrid}.png")
                                        download_image(path_image_me, content_node.text)
                                        if os.path.isfile(path_image_me):
                                            path_image = path_image_me
                                            logger.debug(f"[Ref] Successfully downloaded image to: {path_image}")
                                        else:
                                            logger.error("[Ref] Failed to download image: file not created")
                                    else:
                                        logger.error("[Ref] No content found in refermsg")
                                except Exception as e:
                                    logger.error(f"[Ref] Error downloading image: {str(e)}")
                                
                                if not path_image:
                                    path_image_me = os.path.join(TmpDir().path(), f"{svrid}.png")
                                    logger.debug(f"[Ref] Checking file: {path_image_me}")
                                    if os.path.isfile(path_image_me):
                                        path_image = path_image_me
                                        logger.debug(f"[Ref] Found image file: {path_image}")
                            
                            if not path_image or not os.path.isfile(path_image):
                                logger.error(f"[Ref] Image path not ready or file not found: {path_image}")
                                set_reply_text("图片未准备好或无法访问", e_context, ReplyType.ERROR)
                                return

                            try:
                                # 验证图片文件是否可以打开
                                with Image.open(path_image) as img:
                                    img.verify()
                                logger.debug(f"[Ref] Image verified successfully: {path_image}")
                            except Exception as e:
                                logger.error(f"[Ref] Invalid image file: {str(e)}")
                                set_reply_text("图片文件无效", e_context, ReplyType.ERROR)
                                return

                            if title_text in filters.keys():
                                try:
                                    with open(path_image, "rb") as img_file:
                                        byte_arr = io.BytesIO(img_file.read())
                                        byte_arr.seek(0)
                                        filter_img = Image.open(byte_arr).filter(filters[title_text])
                                        buf = io.BytesIO()
                                        filter_img.save(buf, format="PNG")
                                        buf.seek(0)
                                        reply = Reply()
                                        reply.type = ReplyType.IMAGE
                                        reply.content = buf
                                        e_context["reply"] = reply
                                        e_context.action = EventAction.BREAK_PASS
                                        logger.info(f"[Ref] Successfully applied filter {title_text}")
                                except Exception as e:
                                    logger.error(f"[Ref] Error applying filter {title_text}: {str(e)}")
                                    set_reply_text(f"应用滤镜 {title_text} 失败", e_context, ReplyType.ERROR)
                                    return
                            else:
                                try:
                                    from zhipuai import ZhipuAI
                                    
                                    zhipu_api_key = self.zhipu_api_key
                                    if is_none_or_empty(zhipu_api_key):
                                        set_reply_text("图像理解api key未设置", e_context, ReplyType.INFO)
                                        return
                                    
                                    prompt = title_text
                                    prompts = {
                                        "分析": """请对这张图片进行专业、全面的分析：

1. 图片基本信息
- 场景类型（室内/室外/自然/城市等）
- 拍摄视角和构图特点
- 主要色调和光线效果

2. 主要内容分析
- 核心主体和关键元素
- 人物表情、动作和互动（如果有）
- 环境和背景细节
- 文字内容（如果有）

3. 深度解读
- 图片传达的主要信息或故事
- 情感氛围和画面基调
- 特殊细节或亮点
- 专业技术特点（如构图技巧、摄影技巧等）

4. 综合评价
- 图片的整体质量和效果
- 独特之处和创意亮点
- 可能的拍摄目的或用途

请用清晰的结构和生动的语言描述，突出重点，适当使用emoji点缀，让分析更加直观易懂。""",
                                        
                                        "描述": """请对这张图片进行简洁、生动的描述：

1. 核心内容
- 一句话概括图片主题
- 突出最吸引眼球的元素
- 关键场景或动作描述

2. 重要细节
- 人物、物体的特征
- 环境和氛围
- 有趣或独特的点

请用优美流畅的语言，像讲故事一样描述这张图片，让读者能通过文字"看到"图片。""",
                                        
                                        "鉴赏": """请从专业角度对这张图片进行艺术鉴赏：

1. 艺术构成
- 构图方式和视觉重心
- 色彩搭配和明暗对比
- 空间层次和景深处理

2. 技术评析
- 拍摄参数推测（如光圈、快门等）
- 后期处理痕迹
- 特殊摄影技巧

3. 艺术价值
- 创作意图解读
- 美学特点分析
- 艺术表现力评价

请以专业但不晦涩的语言进行点评，既要体现专业性，也要让普通读者能够理解。""",
                                        
                                        "总结": """请简明扼要地总结这张图片的要点：

1. 主题内容（一句话概括）
2. 关键元素（3-5个要点）
3. 特别之处（如果有）
4. 整体评价

请用简洁的语言，突出重点，让读者快速理解图片的核心内容。"""
                                    }
                                    if title_text in prompts.keys():
                                        prompt = prompts[title_text]
                                        logger.debug(f"[Ref] Using predefined prompt for {title_text}")
                                    
                                    try:
                                        base64_image = image_to_base64(path_image)
                                        if not base64_image:
                                            raise ValueError("Failed to convert image to base64")
                                        
                                        logger.debug("[Ref] Image converted to base64 successfully")
                                        client = ZhipuAI(api_key=zhipu_api_key)
                                        
                                        logger.debug(f"[Ref] Sending request to ZhipuAI with model: {self.zhipu_image_model}")
                                        response = client.chat.completions.create(
                                            model=self.zhipu_image_model,
                                            messages=[
                                                {
                                                    "role": "user",
                                                    "content": [
                                                        {"type": "text", "text": prompt},
                                                        {
                                                            "type": "image_url",
                                                            "image_url": {
                                                                "url": f"data:image/jpeg;base64,{base64_image}"
                                                            },
                                                        },
                                                    ],
                                                }
                                            ],
                                        )
                                        
                                        if not response or not response.choices:
                                            raise ValueError("Empty response from ZhipuAI")
                                            
                                        finish_reason = response.choices[0].finish_reason
                                        if finish_reason != "stop":
                                            raise ValueError(f"Unexpected finish reason: {finish_reason}")
                                            
                                        reply_content = response.choices[0].message.content
                                        if not reply_content:
                                            raise ValueError("Empty reply content from ZhipuAI")
                                            
                                        logger.info("[Ref] Successfully got analysis from ZhipuAI")
                                        set_reply_text(reply_content, e_context, ReplyType.TEXT)
                                        
                                    except Exception as e:
                                        logger.error(f"[Ref] Error in ZhipuAI processing: {str(e)}")
                                        set_reply_text("图像分析处理失败", e_context, ReplyType.ERROR)
                                        return
                                        
                                except Exception as e:
                                    logger.error(f"[Ref] Error in image analysis setup: {str(e)}")
                                    set_reply_text("图像分析初始化失败", e_context, ReplyType.ERROR)
                                    return
                        except Exception as e:
                            logger.error(f"[Ref] Error processing image: {str(e)}")
                            return
                            
                    elif ref_type == "47":  # emoji
                        if title_text in ["下载"]:
                            try:
                                import html
                                import re
                                
                                ref_emoji_msg = self.msg_cache.get(svrid)
                                if ref_emoji_msg is None:
                                    logger.error("[Ref] Emoji message not found in cache")
                                    return
                                raw_msg = ref_emoji_msg.msg
                                content_xml = raw_msg["Data"]["Content"]["string"]
                                match = re.search(r'cdnurl\s*=\s*"([^"]+)"', content_xml)
                                if match:
                                    cdnurl = match.group(1)
                                    cdnurl = html.unescape(cdnurl)
                                    logger.info(f"[Ref]got emoji, url: {cdnurl}")
                                    reply = Reply()
                                    reply.type = ReplyType.IMAGE_URL
                                    reply.content = cdnurl
                                    e_context["reply"] = reply
                                    e_context.action = EventAction.BREAK_PASS
                                else:
                                    logger.error("[Ref] No CDN URL found in emoji content")
                            except Exception as e:
                                logger.error(f"[Ref] Error processing emoji: {str(e)}")
                                return
                except Exception as e:
                    logger.error(f"[Ref] Error processing XML content: {str(e)}")
                    return
                    
            elif msg_type == 1:  # TEXT
                context = e_context["context"]
                content = context.content
                if content.startswith("画"):
                    try:
                        from zhipuai import ZhipuAI
                        
                        zhipu_api_key = self.zhipu_api_key
                        if is_none_or_empty(zhipu_api_key):
                            set_reply_text("图像生成api key未设置", e_context, ReplyType.INFO)
                            return
                            
                        client = ZhipuAI(api_key=zhipu_api_key)
                        response = client.images.generations(
                            model="cogview-3-flash",
                            prompt=content,
                        )
                        
                        image_url = response.data[0].url
                        reply = Reply()
                        reply.type = ReplyType.IMAGE_URL
                        reply.content = image_url
                        logger.info(f"draw image: ({content}, {image_url})")
                        e_context["reply"] = reply
                        e_context.action = EventAction.BREAK_PASS
                    except Exception as e:
                        logger.error(f"[Ref] Error generating image: {str(e)}")
                        set_reply_text("图像生成失败", e_context, ReplyType.ERROR)
                        return
                        
        except Exception as e:
            logger.error(f"[Ref] Error in handle_ref: {str(e)}")
            return

    def on_handle_context(self, e_context: EventContext):
        if e_context["context"].type not in [
            ContextType.TEXT,
            ContextType.IMAGE,
            ContextType.EMOJI,
            ContextType.SHARING,
        ]:
            return
        try:
            msg: ChatMessage = e_context["context"]["msg"]
            content = e_context["context"].content
            logger.debug("[Ref] on_handle_context. content: %s" % content)
            self.handle_ref(e_context)
        except Exception as e:
            logger.error(f"[Ref] Error in on_handle_context: {str(e)}")
            return

    def get_help_text(self, **kwargs):
        help_text = """📱 智能引用助手 v0.1

🔍 基础功能：
1. 文字引用
   - 举牌/举：生成可爱的小人举牌图片，适合表达心情或传递信息
   - 总结：对引用的聊天记录进行精炼总结，提取关键信息
   - 分析：对引用的聊天记录进行深入分析，包括互动模式、情感倾向等
   
2. 图片处理
   - 分析：智能解读图片内容，提供专业的图像分析
   - 描述：生动描述图片的主要内容和细节
   - 鉴赏：从专业角度对图片进行艺术评析
   - 总结：简明扼要地概括图片要点

3. 图片滤镜（支持引用任意图片）
   - 模糊效果：BLUR
   - 轮廓提取：CONTOUR
   - 细节增强：DETAIL
   - 边缘增强：EDGE_ENHANCE, EDGE_ENHANCE_MORE
   - 浮雕效果：EMBOSS
   - 边缘检测：FIND_EDGES
   - 锐化处理：SHARPEN
   - 平滑效果：SMOOTH, SMOOTH_MORE

4. 表情处理
   - 下载：支持下载图片或GIF格式的表情

5. AI创作
   - 画 + 描述：基于智谱AI的文本生图功能

💡 使用技巧：
- 引用时直接输入命令即可，如引用图片时输入"分析"
- 引用聊天记录时输入"总结"或"分析"进行智能分析
- 文生图时使用"画 + 描述"格式，如"画 春天的樱花"
- 支持群聊和私聊场景

🎯 聊天记录分析功能：
1. 总结模式
   - 提取核心对话内容和关键信息
   - 用简洁的语言概括对话主题
   - 突出重要的结论或决定
   - 适合快速了解对话要点

2. 分析模式
   - 分析对话的目的和背景
   - 提取关键论点和观点
   - 识别情感倾向和态度
   - 总结互动模式和群组动态
   - 适合深入理解对话内容

⚙️ 注意事项：
- 部分功能需要配置智谱API密钥
- 图片分析等功能可能需要较短的处理时间
- 建议上传清晰的图片以获得更好的分析效果
- 聊天记录分析支持单聊和群聊场景"""
        return help_text

    def _load_config_template(self):
        logger.info("[Ref]use config.json.template")
        try:
            plugin_config_path = os.path.join(self.path, "config.json.template")
            if os.path.exists(plugin_config_path):
                with open(plugin_config_path, "r", encoding="utf-8") as f:
                    plugin_conf = json.load(f)
                    return plugin_conf
        except Exception as e:
            logger.exception(e)
