# encoding:utf-8
import io
import os
import time
import json
import plugins
from bridge.context import ContextType
from bridge.reply import Reply, ReplyType
from channel.chat_message import ChatMessage
from common.log import logger
from plugins import *
from config import conf
from .misc import *
import xml.etree.ElementTree as ET
from common.expired_dict import ExpiredDict
from PIL import Image, ImageFilter
from io import BytesIO

from common.tmp_dir import TmpDir


@plugins.register(
    name="Ref",
    desire_priority=100,  # 提高优先级，确保在其他插件之前处理引用消息
    hidden=True,
    desc="智能引用助手 - 支持WechatPadPro通道的引用消息处理",
    version="0.2",
    author="fred (adapted for WechatPadPro)",
)
class Ref(Plugin):
    def __init__(self):
        super().__init__()
        try:
            self.config = super().load_config()
            if not self.config:
                self.config = self._load_config_template()
            self.zhipu_api_key = self.config.get("zhipu_api_key")
            self.zhipu_image_model = self.config.get("zhipu_image_model", "GLM-4V-Flash")
            logger.info("[Ref] WechatPadPro适配版本初始化完成")
            self.handlers[Event.ON_HANDLE_CONTEXT] = self.on_handle_context
        except Exception as e:
            logger.error(f"[Ref]初始化异常：{e}")
            raise "[Ref] init failed, ignore "

        # 使用WechatPadPro兼容的消息缓存
        self.msg_cache = ExpiredDict(60 * 3)  # 3分钟缓存
        self.image_cache = ExpiredDict(60 * 10)  # 10分钟图片缓存

    def handle_ref(self, e_context):
        """处理引用消息 - WechatPadPro适配版本"""
        try:
            # 移除通道限制，支持WechatPadPro通道
            msg = e_context["context"]["msg"]

            # 适配WechatPadPro消息格式
            if hasattr(msg, 'msg_type'):
                msg_type = msg.msg_type
            else:
                msg_type = getattr(msg, 'type', 0)

            msg_id = getattr(msg, 'msg_id', str(int(time.time())))

            # 缓存图片和表情消息 - 适配WechatPadPro消息类型
            if msg_type in [3, "3"] or msg_type in [47, "47"]:  # 图片或表情
                self.msg_cache[msg_id] = msg
                logger.debug(f"[Ref] 缓存消息: ID={msg_id}, 类型={msg_type}")

            # 处理XML类型消息（引用消息）- 适配WechatPadPro
            if msg_type in [49, "49"] or (hasattr(msg, 'ctype') and msg.ctype == ContextType.XML):
                try:
                    # 适配WechatPadPro消息内容格式
                    if hasattr(msg, 'content'):
                        content_xml = msg.content
                    else:
                        logger.error("[Ref] 无法获取消息内容")
                        return

                    # 检查是否已经被WechatPadPro处理过的引用消息
                    if hasattr(msg, 'is_processed_text_quote') and msg.is_processed_text_quote:
                        # 处理文本引用
                        self._handle_text_quote(e_context, msg)
                        return
                    elif hasattr(msg, 'is_processed_image_quote') and msg.is_processed_image_quote:
                        # 处理图片引用
                        self._handle_image_quote(e_context, msg)
                        return

                    # 如果没有被预处理，尝试手动解析XML
                    if not content_xml.strip().startswith('<'):
                        logger.debug("[Ref] 非XML格式消息，跳过处理")
                        return

                    try:
                        root = ET.fromstring(content_xml)
                    except ET.ParseError as e:
                        logger.error(f"[Ref] XML解析错误: {str(e)}")
                        return

                    appmsg = root.find("appmsg")
                    if appmsg is None:
                        logger.debug("[Ref] 非引用消息XML结构")
                        return

                    xml_type = appmsg.find("type")
                    if xml_type is None or xml_type.text != "57":
                        logger.debug("[Ref] 非引用消息类型")
                        return

                    title = appmsg.find("title")
                    if title is None or not title.text:
                        logger.debug("[Ref] 引用消息无标题")
                        return
                    title_text = title.text.strip()

                    # 清理群聊消息中的@前缀
                    if hasattr(msg, 'is_group') and msg.is_group:
                        import re
                        title_text = re.sub(r"@[^\u2005]+\u2005", "", title_text)
                        title_text = re.sub(r'^[a-zA-Z0-9]\s+', '', title_text).strip()
                        title_text = re.sub(r'^@?[\u4e00-\u9fff\w]+\s+', '', title_text).strip()

                    refermsg = appmsg.find("refermsg")
                    if refermsg is None:
                        logger.debug("[Ref] 引用消息无refermsg节点")
                        return

                    ref_type = refermsg.find("type")
                    svrid = refermsg.find("svrid")
                    if ref_type is None:
                        logger.debug("[Ref] 引用消息无类型")
                        return

                    ref_type = ref_type.text
                    svrid = svrid.text if svrid is not None else ""
                    
                    # 处理不同类型的引用消息
                    if ref_type == "1":  # 文本引用
                        self._handle_manual_text_quote(e_context, title_text, refermsg)

                    elif ref_type == "3":  # 图片引用
                        self._handle_manual_image_quote(e_context, title_text, refermsg, svrid)

                    elif ref_type == "49":  # 聊天记录引用
                        self._handle_manual_chat_quote(e_context, title_text, refermsg)

                    elif ref_type == "47":  # 表情引用
                        self._handle_manual_emoji_quote(e_context, title_text, refermsg, svrid)

                except Exception as e:
                    logger.error(f"[Ref] XML消息处理错误: {str(e)}")
                    return

            # 处理TEXT类型消息（包括WechatPadPro预处理的引用消息和文生图）
            elif msg_type in [1, "1"] or (hasattr(msg, 'ctype') and msg.ctype == ContextType.TEXT):
                # 检查是否是WechatPadPro预处理的引用消息
                if hasattr(msg, 'is_processed_text_quote') and msg.is_processed_text_quote:
                    logger.info("[Ref] 检测到WechatPadPro预处理的文本引用消息")
                    self._handle_text_quote(e_context, msg)
                    return
                elif hasattr(msg, 'is_processed_image_quote') and msg.is_processed_image_quote:
                    logger.info("[Ref] 检测到WechatPadPro预处理的图片引用消息")
                    self._handle_image_quote(e_context, msg)
                    return

                # 处理文生图功能
                context = e_context["context"]
                content = context.content
                if content.startswith("画"):
                    self._handle_text_to_image(e_context, content)

        except Exception as e:
            logger.error(f"[Ref] handle_ref处理错误: {str(e)}")
            return

    def _handle_text_quote(self, e_context, msg):
        """处理WechatPadPro预处理的文本引用"""
        try:
            # 从WechatPadPro处理后的内容中提取用户问题和被引用的文本
            content = getattr(msg, 'content', '')

            # 解析WechatPadPro格式的引用消息
            import re
            match = re.match(r'用户针对以下(?:消息|聊天记录)提问："(.*?)"\n\n被引用的消息来自".*?"：\n"(.*?)"\n\n', content, re.DOTALL)
            if match:
                user_question = match.group(1).strip()
                quoted_text = match.group(2).strip()

                logger.info(f"[Ref] 解析引用消息 - 用户问题: '{user_question}', 被引用内容: '{quoted_text}'")

                if user_question in ["举", "举牌"]:
                    self._generate_card_image(e_context, quoted_text)
                    return

            # 如果解析失败，尝试直接从content获取
            if content in ["举", "举牌"]:
                quoted_text = getattr(msg, 'quoted_text', '')
                if quoted_text:
                    self._generate_card_image(e_context, quoted_text)
                else:
                    set_reply_text("无法获取引用的文本内容", e_context, ReplyType.ERROR)
        except Exception as e:
            logger.error(f"[Ref] 处理文本引用错误: {str(e)}")

    def _handle_image_quote(self, e_context, msg):
        """处理WechatPadPro预处理的图片引用"""
        try:
            title = getattr(msg, 'content', '')
            image_path = getattr(msg, 'referenced_image_path', '')

            if not image_path or not os.path.exists(image_path):
                set_reply_text("引用的图片不可用", e_context, ReplyType.ERROR)
                return

            # 图片滤镜处理
            filters = {
                "BLUR": ImageFilter.BLUR,
                "CONTOUR": ImageFilter.CONTOUR,
                "DETAIL": ImageFilter.DETAIL,
                "EDGE_ENHANCE": ImageFilter.EDGE_ENHANCE,
                "EDGE_ENHANCE_MORE": ImageFilter.EDGE_ENHANCE_MORE,
                "EMBOSS": ImageFilter.EMBOSS,
                "FIND_EDGES": ImageFilter.FIND_EDGES,
                "SHARPEN": ImageFilter.SHARPEN,
                "SMOOTH": ImageFilter.SMOOTH,
                "SMOOTH_MORE": ImageFilter.SMOOTH_MORE,
            }

            if title in filters:
                self._apply_image_filter(e_context, image_path, filters[title], title)
            else:
                # 图片分析
                self._analyze_image_with_ai(e_context, image_path, title)

        except Exception as e:
            logger.error(f"[Ref] 处理图片引用错误: {str(e)}")

    def _handle_manual_text_quote(self, e_context, title_text, refermsg):
        """手动处理文本引用（XML解析方式）"""
        try:
            if title_text in ["举", "举牌"]:
                quoted_content = refermsg.find("content")
                if quoted_content is not None and quoted_content.text:
                    self._generate_card_image(e_context, quoted_content.text)
                else:
                    set_reply_text("无法获取引用的文本内容", e_context, ReplyType.ERROR)
        except Exception as e:
            logger.error(f"[Ref] 手动处理文本引用错误: {str(e)}")

    def _handle_manual_image_quote(self, e_context, title_text, refermsg, svrid):
        """手动处理图片引用（XML解析方式）"""
        try:
            # 尝试从缓存获取图片
            path_image = self._get_image_from_cache_or_download(refermsg, svrid)

            if not path_image:
                set_reply_text("图片未准备好或无法访问", e_context, ReplyType.ERROR)
                return

            # 图片滤镜处理
            filters = {
                "BLUR": ImageFilter.BLUR,
                "CONTOUR": ImageFilter.CONTOUR,
                "DETAIL": ImageFilter.DETAIL,
                "EDGE_ENHANCE": ImageFilter.EDGE_ENHANCE,
                "EDGE_ENHANCE_MORE": ImageFilter.EDGE_ENHANCE_MORE,
                "EMBOSS": ImageFilter.EMBOSS,
                "FIND_EDGES": ImageFilter.FIND_EDGES,
                "SHARPEN": ImageFilter.SHARPEN,
                "SMOOTH": ImageFilter.SMOOTH,
                "SMOOTH_MORE": ImageFilter.SMOOTH_MORE,
            }

            if title_text in filters:
                self._apply_image_filter(e_context, path_image, filters[title_text], title_text)
            else:
                # 图片分析
                self._analyze_image_with_ai(e_context, path_image, title_text)

        except Exception as e:
            logger.error(f"[Ref] 手动处理图片引用错误: {str(e)}")

    def _handle_manual_chat_quote(self, e_context, title_text, refermsg):
        """手动处理聊天记录引用（XML解析方式）"""
        try:
            if title_text in ["总结", "分析"]:
                content_node = refermsg.find("content")
                if content_node is not None and content_node.text:
                    inner_msg = ET.fromstring(content_node.text)
                    inner_appmsg = inner_msg.find("appmsg")
                    if inner_appmsg is not None:
                        des = inner_appmsg.find("des")
                        if des is not None and des.text:
                            chat_content = des.text.strip()
                            self._analyze_chat_with_ai(e_context, chat_content, title_text)
        except Exception as e:
            logger.error(f"[Ref] 手动处理聊天记录引用错误: {str(e)}")

    def _handle_manual_emoji_quote(self, e_context, title_text, refermsg, svrid):
        """手动处理表情引用（XML解析方式）"""
        try:
            if title_text in ["下载"]:
                ref_emoji_msg = self.msg_cache.get(svrid)
                if ref_emoji_msg is None:
                    set_reply_text("表情消息未找到", e_context, ReplyType.ERROR)
                    return

                # 适配WechatPadPro消息格式
                if hasattr(ref_emoji_msg, 'content'):
                    content_xml = ref_emoji_msg.content
                else:
                    set_reply_text("无法获取表情内容", e_context, ReplyType.ERROR)
                    return

                import html
                import re
                import requests
                import tempfile

                match = re.search(r'cdnurl\s*=\s*"([^"]+)"', content_xml)
                if match:
                    cdnurl = match.group(1)
                    cdnurl = html.unescape(cdnurl)
                    logger.info(f"[Ref] 获取表情URL: {cdnurl}")

                    # 下载表情到本地文件
                    try:
                        emoji_response = requests.get(cdnurl, timeout=30)
                        emoji_response.raise_for_status()

                        # 根据URL判断文件类型
                        if '.gif' in cdnurl.lower():
                            suffix = '.gif'
                        else:
                            suffix = '.png'

                        # 创建临时文件
                        with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp_file:
                            tmp_file.write(emoji_response.content)
                            tmp_file_path = tmp_file.name

                        reply = Reply()
                        reply.type = ReplyType.IMAGE
                        reply.content = tmp_file_path
                        e_context["reply"] = reply
                        e_context.action = EventAction.BREAK_PASS
                        logger.info(f"[Ref] 表情已下载到本地: {tmp_file_path}")

                    except Exception as download_error:
                        logger.error(f"[Ref] 下载表情失败: {str(download_error)}")
                        # 如果下载失败，尝试直接使用URL
                        reply = Reply()
                        reply.type = ReplyType.IMAGE_URL
                        reply.content = cdnurl
                        e_context["reply"] = reply
                        e_context.action = EventAction.BREAK_PASS
                else:
                    set_reply_text("无法提取表情下载链接", e_context, ReplyType.ERROR)
        except Exception as e:
            logger.error(f"[Ref] 手动处理表情引用错误: {str(e)}")

    def _handle_text_to_image(self, e_context, content):
        """处理文生图功能"""
        try:
            from zhipuai import ZhipuAI
            import requests
            import tempfile

            if is_none_or_empty(self.zhipu_api_key):
                set_reply_text("图像生成API key未设置", e_context, ReplyType.INFO)
                return

            client = ZhipuAI(api_key=self.zhipu_api_key)
            response = client.images.generations(
                model="cogview-3-flash",
                prompt=content,
            )

            image_url = response.data[0].url
            logger.info(f"[Ref] 生成图片: ({content}, {image_url})")

            # 下载图片到本地临时文件，避免URL发送问题
            try:
                img_response = requests.get(image_url, timeout=30)
                img_response.raise_for_status()

                # 创建临时文件
                with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as tmp_file:
                    tmp_file.write(img_response.content)
                    tmp_file_path = tmp_file.name

                # 使用本地文件路径发送
                reply = Reply()
                reply.type = ReplyType.IMAGE
                reply.content = tmp_file_path
                e_context["reply"] = reply
                e_context.action = EventAction.BREAK_PASS
                logger.info(f"[Ref] 图片已下载到本地: {tmp_file_path}")

            except Exception as download_error:
                logger.error(f"[Ref] 下载生成的图片失败: {str(download_error)}")
                # 如果下载失败，尝试直接使用URL
                reply = Reply()
                reply.type = ReplyType.IMAGE_URL
                reply.content = image_url
                e_context["reply"] = reply
                e_context.action = EventAction.BREAK_PASS

        except Exception as e:
            logger.error(f"[Ref] 图像生成错误: {str(e)}")
            set_reply_text("图像生成失败", e_context, ReplyType.ERROR)

    def _generate_card_image(self, e_context, text):
        """生成举牌图片"""
        try:
            image_url = get_card_image_url(text)
            if image_url:
                image_data = download_image_by_url(image_url)
                if image_data:
                    image_data.seek(0)
                    image = Image.open(image_data)
                    jpg_image_data = BytesIO()
                    image.convert("RGB").save(jpg_image_data, format="JPEG")
                    jpg_image_data.seek(0)

                    reply = Reply()
                    reply.type = ReplyType.IMAGE
                    reply.content = jpg_image_data
                    e_context["reply"] = reply
                    e_context.action = EventAction.BREAK_PASS
                    logger.info("[Ref] 成功生成举牌图片")
                else:
                    set_reply_text("举牌图片生成失败", e_context, ReplyType.ERROR)
            else:
                set_reply_text("举牌服务不可用", e_context, ReplyType.ERROR)
        except Exception as e:
            logger.error(f"[Ref] 生成举牌图片错误: {str(e)}")
            set_reply_text("举牌图片生成失败", e_context, ReplyType.ERROR)

    def _apply_image_filter(self, e_context, image_path, filter_type, filter_name):
        """应用图片滤镜"""
        try:
            with open(image_path, "rb") as img_file:
                byte_arr = BytesIO(img_file.read())
                byte_arr.seek(0)
                filter_img = Image.open(byte_arr).filter(filter_type)
                buf = BytesIO()
                filter_img.save(buf, format="PNG")
                buf.seek(0)
                reply = Reply()
                reply.type = ReplyType.IMAGE
                reply.content = buf
                e_context["reply"] = reply
                e_context.action = EventAction.BREAK_PASS
                logger.info(f"[Ref] 成功应用滤镜: {filter_name}")
        except Exception as e:
            logger.error(f"[Ref] 应用滤镜错误: {str(e)}")
            set_reply_text(f"应用滤镜 {filter_name} 失败", e_context, ReplyType.ERROR)

    def _analyze_image_with_ai(self, e_context, image_path, title_text):
        """使用AI分析图片"""
        try:
            from zhipuai import ZhipuAI

            if is_none_or_empty(self.zhipu_api_key):
                set_reply_text("图像理解API key未设置", e_context, ReplyType.INFO)
                return

            # 预定义的分析提示词
            prompts = {
                "分析": """请对这张图片进行专业、全面的分析：

1. 图片基本信息
- 场景类型（室内/室外/自然/城市等）
- 拍摄视角和构图特点
- 主要色调和光线效果

2. 主要内容分析
- 核心主体和关键元素
- 人物表情、动作和互动（如果有）
- 环境和背景细节
- 文字内容（如果有）

3. 深度解读
- 图片传达的主要信息或故事
- 情感氛围和画面基调
- 特殊细节或亮点
- 专业技术特点（如构图技巧、摄影技巧等）

4. 综合评价
- 图片的整体质量和效果
- 独特之处和创意亮点
- 可能的拍摄目的或用途

请用清晰的结构和生动的语言描述，突出重点，适当使用emoji点缀，让分析更加直观易懂。""",

                "描述": """请对这张图片进行简洁、生动的描述：

1. 核心内容
- 一句话概括图片主题
- 突出最吸引眼球的元素
- 关键场景或动作描述

2. 重要细节
- 人物、物体的特征
- 环境和氛围
- 有趣或独特的点

请用优美流畅的语言，像讲故事一样描述这张图片，让读者能通过文字"看到"图片。""",

                "鉴赏": """请从专业角度对这张图片进行艺术鉴赏：

1. 艺术构成
- 构图方式和视觉重心
- 色彩搭配和明暗对比
- 空间层次和景深处理

2. 技术评析
- 拍摄参数推测（如光圈、快门等）
- 后期处理痕迹
- 特殊摄影技巧

3. 艺术价值
- 创作意图解读
- 美学特点分析
- 艺术表现力评价

请以专业但不晦涩的语言进行点评，既要体现专业性，也要让普通读者能够理解。""",

                "总结": """请简明扼要地总结这张图片的要点：

1. 主题内容（一句话概括）
2. 关键元素（3-5个要点）
3. 特别之处（如果有）
4. 整体评价

请用简洁的语言，突出重点，让读者快速理解图片的核心内容。"""
            }

            prompt = prompts.get(title_text, title_text)

            base64_image = image_to_base64(image_path)
            if not base64_image:
                set_reply_text("图片处理失败", e_context, ReplyType.ERROR)
                return

            client = ZhipuAI(api_key=self.zhipu_api_key)
            response = client.chat.completions.create(
                model=self.zhipu_image_model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                },
                            },
                        ],
                    }
                ],
            )

            if response and response.choices and response.choices[0].finish_reason == "stop":
                reply_content = response.choices[0].message.content
                if reply_content:
                    set_reply_text(reply_content, e_context, ReplyType.TEXT)
                    logger.info("[Ref] 成功完成图片AI分析")
                else:
                    set_reply_text("AI分析结果为空", e_context, ReplyType.ERROR)
            else:
                set_reply_text("AI分析失败", e_context, ReplyType.ERROR)

        except Exception as e:
            logger.error(f"[Ref] AI图片分析错误: {str(e)}")
            set_reply_text("图片分析失败", e_context, ReplyType.ERROR)

    def _analyze_chat_with_ai(self, e_context, chat_content, title_text):
        """使用AI分析聊天记录"""
        try:
            from zhipuai import ZhipuAI

            if is_none_or_empty(self.zhipu_api_key):
                set_reply_text("API key未设置", e_context, ReplyType.INFO)
                return

            prompts = {
                "总结": """请对以下聊天记录进行精炼总结：
1. 提取核心对话内容和关键信息
2. 用简洁的语言概括对话主题
3. 突出重要的结论或决定
4. 保持客观中立的语气
5. 如果是群聊，注意总结各方观点

聊天记录内容如下：
{content}""",
                "分析": """请对以下聊天记录进行深入分析：
1. 分析对话的主要目的和背景
2. 提取对话中的关键论点和观点
3. 识别对话中的情感倾向和态度
4. 总结对话各方的互动模式
5. 如果有争议，分析各方立场
6. 对对话的效果和结果进行评估
7. 如果是群聊，分析群组互动动态

聊天记录内容如下：
{content}"""
            }

            prompt = prompts.get(title_text, "请分析以下聊天记录的内容，总结对话要点：\n{content}")
            prompt = prompt.format(content=chat_content)

            client = ZhipuAI(api_key=self.zhipu_api_key)
            response = client.chat.completions.create(
                model="glm-4-flash",
                messages=[
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
            )

            if response and response.choices and response.choices[0].finish_reason == "stop":
                reply_content = response.choices[0].message.content
                if reply_content:
                    set_reply_text(reply_content, e_context, ReplyType.TEXT)
                    logger.info("[Ref] 成功完成聊天记录AI分析")
                else:
                    set_reply_text("AI分析结果为空", e_context, ReplyType.ERROR)
            else:
                set_reply_text("聊天记录分析失败", e_context, ReplyType.ERROR)

        except Exception as e:
            logger.error(f"[Ref] AI聊天记录分析错误: {str(e)}")
            set_reply_text("聊天记录分析失败", e_context, ReplyType.ERROR)

    def _get_image_from_cache_or_download(self, refermsg, svrid):
        """从缓存获取图片或尝试下载"""
        try:
            # 首先尝试从消息缓存获取
            ref_image_msg = self.msg_cache.get(svrid)
            if ref_image_msg:
                # 适配WechatPadPro消息格式
                if hasattr(ref_image_msg, 'content') and os.path.exists(ref_image_msg.content):
                    logger.debug(f"[Ref] 从缓存获取图片: {ref_image_msg.content}")
                    return ref_image_msg.content

            # 尝试从WechatPadPro图片缓存目录查找
            # 这里需要适配WechatPadPro的图片缓存机制
            cache_dir = os.path.join(os.getcwd(), "tmp", "wechatpadpro_img_cache")
            if os.path.exists(cache_dir):
                # 查找可能的图片文件
                for ext in ['.jpg', '.jpeg', '.png', '.gif']:
                    possible_path = os.path.join(cache_dir, f"img_{svrid}{ext}")
                    if os.path.exists(possible_path):
                        logger.debug(f"[Ref] 从WechatPadPro缓存获取图片: {possible_path}")
                        return possible_path

            # 如果都没找到，返回None
            logger.warning(f"[Ref] 未找到图片: svrid={svrid}")
            return None

        except Exception as e:
            logger.error(f"[Ref] 获取图片错误: {str(e)}")
            return None

    def on_handle_context(self, e_context: EventContext):
        if e_context["context"].type not in [
            ContextType.TEXT,
            ContextType.IMAGE,
            ContextType.EMOJI,
            ContextType.SHARING,
        ]:
            return
        try:
            msg: ChatMessage = e_context["context"]["msg"]
            content = e_context["context"].content
            logger.debug("[Ref] on_handle_context. content: %s" % content)
            self.handle_ref(e_context)
        except Exception as e:
            logger.error(f"[Ref] Error in on_handle_context: {str(e)}")
            return

    def get_help_text(self, **kwargs):
        help_text = """📱 智能引用助手 v0.2 (WechatPadPro适配版)

🔍 基础功能：
1. 文字引用
   - 举牌/举：生成可爱的小人举牌图片，适合表达心情或传递信息
   - 总结：对引用的聊天记录进行精炼总结，提取关键信息
   - 分析：对引用的聊天记录进行深入分析，包括互动模式、情感倾向等

2. 图片处理
   - 分析：智能解读图片内容，提供专业的图像分析
   - 描述：生动描述图片的主要内容和细节
   - 鉴赏：从专业角度对图片进行艺术评析
   - 总结：简明扼要地概括图片要点

3. 图片滤镜（支持引用任意图片）
   - 模糊效果：BLUR
   - 轮廓提取：CONTOUR
   - 细节增强：DETAIL
   - 边缘增强：EDGE_ENHANCE, EDGE_ENHANCE_MORE
   - 浮雕效果：EMBOSS
   - 边缘检测：FIND_EDGES
   - 锐化处理：SHARPEN
   - 平滑效果：SMOOTH, SMOOTH_MORE

4. 表情处理
   - 下载：支持下载图片或GIF格式的表情

5. AI创作
   - 画 + 描述：基于智谱AI的文本生图功能

💡 使用技巧：
- 引用时直接输入命令即可，如引用图片时输入"分析"
- 引用聊天记录时输入"总结"或"分析"进行智能分析
- 文生图时使用"画 + 描述"格式，如"画 春天的樱花"
- 支持WechatPadPro通道的群聊和私聊场景

🎯 聊天记录分析功能：
1. 总结模式
   - 提取核心对话内容和关键信息
   - 用简洁的语言概括对话主题
   - 突出重要的结论或决定
   - 适合快速了解对话要点

2. 分析模式
   - 分析对话的目的和背景
   - 提取关键论点和观点
   - 识别情感倾向和态度
   - 总结互动模式和群组动态
   - 适合深入理解对话内容

⚙️ 注意事项：
- 已适配WechatPadPro通道，移除了Gewechat通道限制
- 部分功能需要配置智谱API密钥
- 图片分析等功能可能需要较短的处理时间
- 建议上传清晰的图片以获得更好的分析效果
- 聊天记录分析支持单聊和群聊场景
- 利用WechatPadPro的图片缓存机制提高性能"""
        return help_text

    def _load_config_template(self):
        logger.info("[Ref]use config.json.template")
        try:
            plugin_config_path = os.path.join(self.path, "config.json.template")
            if os.path.exists(plugin_config_path):
                with open(plugin_config_path, "r", encoding="utf-8") as f:
                    plugin_conf = json.load(f)
                    return plugin_conf
        except Exception as e:
            logger.exception(e)
